"""
Command Line Interface for CMOST (Colorectal Microsimulation Outcomes Screening Tool).

This module provides command-line access to CMOST functionality including
simulation, calibration, and result analysis.
"""

import click
import os
import sys
import json
import logging
from pathlib import Path
from typing import Optional, List

from .core.simulation import Simulation
from .config.settings import Settings, settings
from .utils.statistics import calculate_statistics
from .utils.visualization import plot_results
from .utils.file_io import save_results, load_results


@click.group()
@click.option('--verbose', '-v', is_flag=True, help='Enable verbose output')
@click.option('--config', '-c', type=click.Path(exists=True), help='Configuration file path')
@click.pass_context
def main(ctx, verbose, config):
    """CMOST - Colorectal Microsimulation Outcomes Screening Tool"""
    # Ensure that ctx.obj exists and is a dict
    ctx.ensure_object(dict)
    
    # Set up logging
    level = logging.DEBUG if verbose else logging.INFO
    logging.basicConfig(level=level, format='%(asctime)s - %(levelname)s - %(message)s')
    
    # Load configuration if provided
    if config:
        settings.load_settings(config)
        ctx.obj['config_loaded'] = True
    else:
        ctx.obj['config_loaded'] = False


@main.command()
@click.option('--patients', '-p', default=10000, help='Number of patients to simulate')
@click.option('--years', '-y', default=50, help='Number of years to simulate')
@click.option('--output', '-o', type=click.Path(), help='Output file path')
@click.option('--seed', '-s', type=int, help='Random seed for reproducibility')
@click.pass_context
def simulate(ctx, patients, years, output, seed):
    """Run a basic simulation"""
    click.echo(f"Starting CMOST simulation with {patients} patients for {years} years...")
    
    # Set up simulation settings
    sim_settings = Settings()
    sim_settings.set('Number_patients', patients)
    
    if seed is not None:
        sim_settings.set('Simulation.RandomSeed', seed)
        import numpy as np
        import random
        np.random.seed(seed)
        random.seed(seed)
    
    try:
        # Create and run simulation
        simulation = Simulation(sim_settings)
        results = simulation.run(years)
        
        # Display summary
        summary = simulation.get_summary_statistics()
        click.echo("\nSimulation completed successfully!")
        click.echo(f"Total patients: {summary['total_patients']}")
        click.echo(f"Years simulated: {summary['years_simulated']}")
        click.echo(f"Cancer incidence: {summary.get('cancer_incidence', 'N/A')}")
        click.echo(f"Cancer mortality: {summary.get('cancer_mortality', 'N/A')}")
        
        # Save results if output specified
        if output:
            simulation.save_results(output)
            click.echo(f"Results saved to: {output}")
            
    except (ValueError, TypeError) as e:
        click.echo(f"Configuration error: {str(e)}", err=True)
        click.echo("Please check your input parameters.", err=True)
        sys.exit(1)
    except (IOError, OSError) as e:
        click.echo(f"File operation error: {str(e)}", err=True)
        click.echo("Please check file permissions and paths.", err=True)
        sys.exit(1)
    except MemoryError as e:
        click.echo(f"Memory error: {str(e)}", err=True)
        click.echo("Try reducing the number of patients or simulation years.", err=True)
        sys.exit(1)
    except KeyboardInterrupt:
        click.echo("\nSimulation interrupted by user.", err=True)
        sys.exit(1)
    except Exception as e:
        click.echo(f"Unexpected error during simulation: {str(e)}", err=True)
        click.echo("Please report this issue with the error details.", err=True)
        sys.exit(1)


@main.command()
@click.option('--strategies', '-s', default='colonoscopy,fit,sigmoidoscopy', 
              help='Comma-separated list of screening strategies to compare')
@click.option('--patients', '-p', default=10000, help='Number of patients per strategy')
@click.option('--output', '-o', type=click.Path(), help='Output file path')
@click.pass_context
def compare(ctx, strategies, patients, output):
    """Compare different screening strategies"""
    strategy_list = [s.strip() for s in strategies.split(',')]
    click.echo(f"Comparing screening strategies: {', '.join(strategy_list)}")
    
    results = {}
    
    for strategy in strategy_list:
        click.echo(f"\nRunning simulation for {strategy}...")
        
        # Set up settings for this strategy
        sim_settings = Settings()
        sim_settings.set('Number_patients', patients)
        sim_settings.set('Screening.PrimaryTest', strategy.title())
        sim_settings.set('Screening.EnableScreening', True)
        
        try:
            simulation = Simulation(sim_settings)
            simulation.run(50)  # 50 years simulation
            
            summary = simulation.get_summary_statistics()
            results[strategy] = summary
            
            click.echo(f"  Cancer incidence: {summary.get('cancer_incidence', 'N/A')}")
            click.echo(f"  Cancer mortality: {summary.get('cancer_mortality', 'N/A')}")
            
        except Exception as e:
            click.echo(f"Error in {strategy} simulation: {str(e)}", err=True)
            continue
    
    # Save comparison results
    if output:
        with open(output, 'w') as f:
            json.dump(results, f, indent=2)
        click.echo(f"\nComparison results saved to: {output}")


@main.command()
@click.option('--target-data', '-t', type=click.Path(exists=True), 
              help='Target epidemiological data file')
@click.option('--output', '-o', type=click.Path(), help='Output file for calibrated parameters')
@click.option('--iterations', '-i', default=100, help='Number of calibration iterations')
@click.pass_context
def calibrate(ctx, target_data, output, iterations):
    """Calibrate model parameters against epidemiological data"""
    click.echo(f"Starting model calibration with {iterations} iterations...")
    
    if not target_data:
        click.echo("Error: Target data file is required for calibration", err=True)
        sys.exit(1)
    
    try:
        # Import calibration module
        from .calibration.auto_calibration import AutoCalibration
        
        # Load target data
        # This would need to be implemented based on data format
        click.echo(f"Loading target data from: {target_data}")
        
        # Run calibration
        calibrator = AutoCalibration()
        calibrated_params = calibrator.calibrate(target_data, iterations)
        
        click.echo("Calibration completed successfully!")
        
        # Save calibrated parameters
        if output:
            with open(output, 'w') as f:
                json.dump(calibrated_params, f, indent=2)
            click.echo(f"Calibrated parameters saved to: {output}")
            
    except ImportError:
        click.echo("Error: Calibration module not available", err=True)
        sys.exit(1)
    except Exception as e:
        click.echo(f"Error during calibration: {str(e)}", err=True)
        sys.exit(1)


@main.command()
@click.argument('results_file', type=click.Path(exists=True))
@click.option('--format', '-f', type=click.Choice(['json', 'excel', 'csv']), 
              default='json', help='Output format')
@click.pass_context
def analyze(ctx, results_file, format):
    """Analyze simulation results"""
    click.echo(f"Analyzing results from: {results_file}")
    
    try:
        # Load results
        with open(results_file, 'r') as f:
            data = json.load(f)
        
        # Display analysis
        if 'summary' in data:
            summary = data['summary']
            click.echo("\nSummary Statistics:")
            for key, value in summary.items():
                click.echo(f"  {key}: {value}")
        
        # Generate additional analysis based on format
        if format == 'excel':
            click.echo("Excel export functionality would be implemented here")
        elif format == 'csv':
            click.echo("CSV export functionality would be implemented here")
            
    except Exception as e:
        click.echo(f"Error analyzing results: {str(e)}", err=True)
        sys.exit(1)


@main.command()
@click.option('--template', '-t', type=click.Choice(['basic', 'screening', 'calibration']),
              default='basic', help='Configuration template type')
@click.option('--output', '-o', type=click.Path(), required=True, help='Output configuration file')
@click.pass_context
def config(ctx, template, output):
    """Generate configuration file templates"""
    click.echo(f"Generating {template} configuration template...")
    
    # Create template settings
    template_settings = Settings()
    
    if template == 'screening':
        template_settings.set('Screening.EnableScreening', True)
        template_settings.set('Screening.PrimaryTest', 'Colonoscopy')
        template_settings.set('Screening.ScreeningAges', [50, 55, 60, 65, 70, 75])
    elif template == 'calibration':
        template_settings.set('Calibration.Enabled', True)
        template_settings.set('Calibration.TargetMetrics', ['incidence', 'mortality'])
    
    # Save template
    try:
        template_settings.save_settings(output)
        click.echo(f"Configuration template saved to: {output}")
    except Exception as e:
        click.echo(f"Error saving template: {str(e)}", err=True)
        sys.exit(1)


@main.command()
@click.pass_context
def version(ctx):
    """Show version information"""
    from . import __version__, __author__
    click.echo(f"CMOST version {__version__}")
    click.echo(f"Author: {__author__}")


if __name__ == '__main__':
    main()
