# CMOST - 结直肠癌微观仿真结果筛查工具

## 项目概述

CMOST (Colorectal Microsimulation Outcomes Screening Tool) 是一个先进的结直肠癌筛查策略评估工具，采用微观仿真技术模拟个体患者的疾病进展过程，评估不同筛查策略的效果和成本效益。

### 核心特性

- **双重架构设计**: 支持自然人群队列和出生队列两种仿真模式
- **多筛查工具组合**: 支持结肠镜、乙状结肠镜、粪便免疫化学试验(FIT)等多种筛查方法
- **非固定筛查周期**: 基于风险评估的动态筛查调度
- **机器学习校准**: 自适应参数校准和模型优化
- **高性能计算**: 并行处理、内存优化、智能缓存等性能优化功能
- **卫生经济学评价**: 全面的成本效益分析

## 项目结构

```
CMOST_Python/
├── cmost/                          # 核心包
│   ├── __init__.py                 # 包初始化
│   ├── cli.py                      # 命令行接口
│   ├── calibration/                # 校准模块
│   │   ├── __init__.py
│   │   ├── deep_neural_network.py  # 深度神经网络校准
│   │   └── parameter_optimization.py
│   ├── cluster/                    # 集群计算
│   │   ├── __init__.py
│   │   ├── job_manager.py          # 作业管理
│   │   └── result_collector.py     # 结果收集
│   ├── config/                     # 配置管理
│   │   ├── __init__.py
│   │   ├── settings.py             # 设置管理
│   │   └── defaults.py             # 默认配置
│   ├── core/                       # 核心仿真
│   │   ├── __init__.py
│   │   ├── simulation.py           # 主仿真引擎
│   │   ├── progression.py          # 疾病进展模型
│   │   ├── population.py           # 人群生成
│   │   ├── birth_cohort.py         # 出生队列
│   │   └── dual_architecture.py    # 双重架构管理
│   ├── economics/                  # 卫生经济学
│   │   ├── __init__.py
│   │   └── health_economics.py     # 经济评价
│   ├── ml/                         # 机器学习
│   │   ├── __init__.py
│   │   ├── adaptive_calibration.py # 自适应校准
│   │   └── integrated_calibration.py
│   ├── models/                     # 数据模型
│   │   ├── __init__.py
│   │   ├── patient.py              # 患者模型
│   │   ├── polyp.py                # 息肉模型
│   │   ├── cancer.py               # 癌症模型
│   │   ├── serrated_lesion.py      # 锯齿状病变
│   │   └── molecular_features.py   # 分子特征
│   ├── screening/                  # 筛查模块
│   │   ├── __init__.py
│   │   ├── strategy_manager.py     # 筛查策略管理
│   │   ├── dynamic_scheduler.py    # 动态调度
│   │   └── risk_questionnaire.py   # 风险问卷
│   ├── ui/                         # 用户界面
│   │   ├── __init__.py
│   │   ├── main_window.py          # 主窗口
│   │   └── simulation_panel.py     # 仿真面板
│   └── utils/                      # 工具模块
│       ├── __init__.py
│       ├── statistics.py           # 统计分析
│       ├── visualization.py        # 可视化
│       ├── file_io.py              # 文件操作
│       ├── performance.py          # 性能监控
│       ├── parallel.py             # 并行处理
│       ├── memory.py               # 内存管理
│       └── cache.py                # 缓存系统
├── examples/                       # 示例代码
│   ├── dual_architecture_demo.py   # 双重架构演示
│   ├── integrated_calibration_demo.py # 校准演示
│   ├── screening_strategies_demo.py # 筛查策略演示
│   └── performance_optimization_demo.py # 性能优化演示
├── tests/                          # 测试代码
│   ├── unit/                       # 单元测试
│   ├── integration/                # 集成测试
│   └── performance/                # 性能测试
├── docs/                           # 文档
│   ├── performance_optimization.md # 性能优化指南
│   └── performance_optimization_summary.md
├── requirements.txt                # 依赖包
├── setup.py                       # 安装脚本
├── pyproject.toml                 # 项目配置
└── README.md                      # 项目说明
```

## 安装和配置

### 系统要求

- Python 3.8+
- NumPy, SciPy, Pandas
- Matplotlib, Seaborn (可视化)
- Scikit-learn, TensorFlow (机器学习)
- PSUtil (性能监控)
- Tkinter (GUI界面)

### 安装方法

#### 从源码安装

```bash
git clone https://github.com/your-repo/cmost.git
cd cmost
pip install -e .
```

#### 使用pip安装

```bash
# 基础安装
pip install cmost

# 完整安装（包含所有可选依赖）
pip install cmost[all]
```

### 依赖包安装

```bash
pip install -r requirements.txt
```

## 快速开始

### 1. 命令行使用

```bash
# 运行基本仿真
cmost simulate --patients 10000 --years 50 --output results.json

# 比较筛查策略
cmost compare --strategies colonoscopy,fit,sigmoidoscopy --output comparison.json

# 参数校准
cmost calibrate --target-data seer_data.csv --output calibrated_params.json
```

### 2. Python API使用

```python
from cmost.core.simulation import Simulation
from cmost.config.settings import Settings

# 创建仿真设置
settings = Settings()
settings.set('Number_patients', 10000)
settings.set('Simulation.EnableMultiprocessing', True)

# 运行仿真
simulation = Simulation(settings)
results = simulation.run(years=50)

# 获取统计结果
stats = simulation.get_summary_statistics()
print(f"癌症发病率: {stats['cancer_incidence_rate']:.2f}/100,000")
```

### 3. 图形界面使用

```python
from cmost.ui.main_window import CMOSTMainWindow
import tkinter as tk

# 启动图形界面
root = tk.Tk()
app = CMOSTMainWindow(root)
root.mainloop()
```

## 核心功能详解

### 1. 双重架构仿真

CMOST支持两种仿真架构：

- **自然人群队列**: 模拟现有人群的筛查效果
- **出生队列**: 模拟新生儿队列的长期筛查效果

```python
from cmost.core.dual_architecture import DualArchitectureManager

# 配置双重架构
config = ArchitectureConfig(
    natural_population_size=100000,
    annual_birth_cohort_size=10000,
    population_split_ratio=0.8
)

manager = DualArchitectureManager(config)
results = manager.run_combined_simulation(years=50)
```

### 2. 筛查策略管理

支持多种筛查策略和动态调度：

```python
from cmost.screening.strategy_manager import ScreeningStrategyManager
from cmost.screening.dynamic_scheduler import DynamicScreeningScheduler

# 创建筛查策略管理器
strategy_manager = ScreeningStrategyManager()

# 应用筛查策略
result = strategy_manager.apply_strategy(
    'colonoscopy_10year',
    patient,
    current_year
)

# 动态筛查调度
scheduler = DynamicScreeningScheduler()
next_screening = scheduler.schedule_next_screening(patient, result, current_year)
```

### 3. 机器学习校准

集成深度学习和传统机器学习方法：

```python
from cmost.ml.integrated_calibration import run_integrated_calibration

# 运行集成校准
calibration_data = {
    'target_incidence': target_data,
    'target_mortality': mortality_data
}

best_params = run_integrated_calibration(
    calibration_data,
    methods=['neural_network', 'random_forest', 'bayesian'],
    auto_select=True
)
```

### 4. 性能优化

#### 并行处理

```python
from cmost.utils.parallel import parallel_simulation

# 并行运行多个仿真
parameter_sets = [
    {'num_patients': 10000, 'screening_strategy': 'colonoscopy'},
    {'num_patients': 10000, 'screening_strategy': 'fit'},
]

results = parallel_simulation(
    simulation_func,
    parameter_sets,
    max_workers=4
)
```

#### 性能监控

```python
from cmost.utils.performance import performance_monitor, profile

# 使用装饰器监控性能
@profile("my_simulation")
def run_simulation():
    # 仿真代码
    pass

# 获取性能报告
metrics = performance_monitor.get_all_metrics()
```

#### 缓存优化

```python
from cmost.utils.cache import cached

# 缓存计算结果
@cached(maxsize=1000, ttl=3600)
def expensive_calculation(params):
    # 耗时计算
    return result
```

### 5. 卫生经济学评价

```python
from cmost.economics.health_economics import HealthEconomicsEvaluator

# 创建经济评价器
evaluator = HealthEconomicsEvaluator()

# 计算成本效益
cost_effectiveness = evaluator.calculate_cost_effectiveness(
    simulation_results,
    time_horizon=50,
    discount_rate=0.03
)

print(f"ICER: ${cost_effectiveness.icer:.2f}/QALY")
```

## 高级功能

### 1. 集群计算

```python
from cmost.cluster.job_manager import JobManager

# 创建作业管理器
job_manager = JobManager(cluster_type='slurm')

# 提交仿真作业
job_id = job_manager.submit_job(
    simulation_settings,
    num_cores=16,
    memory='32G',
    time_limit='24:00:00'
)

# 监控作业状态
status = job_manager.get_job_status(job_id)
```

### 2. 参数扫描

```python
# 定义参数空间
parameter_space = {
    'screening_interval': [5, 10, 15],
    'screening_age_start': [45, 50, 55],
    'screening_age_end': [70, 75, 80]
}

# 提交参数扫描作业
job_ids = job_manager.submit_parameter_sweep(
    base_settings,
    parameter_space,
    job_name_prefix='param_sweep'
)
```

### 3. 结果可视化

```python
from cmost.utils.visualization import plot_results, create_summary_report

# 绘制结果图表
fig, axes = plot_results(simulation_results)

# 创建综合报告
report = create_summary_report(
    simulation_results,
    output_file='simulation_report.html'
)
```

## 配置和定制

### 1. 基本配置

```python
from cmost.config.settings import Settings

settings = Settings()

# 仿真参数
settings.set('Number_patients', 100000)
settings.set('Simulation.MaxAge', 100)
settings.set('Simulation.StartAge', 20)

# 性能优化
settings.set('Simulation.EnableMultiprocessing', True)
settings.set('Simulation.NumProcesses', 8)

# 筛查参数
settings.set('Screening.DefaultStrategy', 'colonoscopy_10year')
settings.set('Screening.ParticipationRate', 0.65)
```

### 2. 模型参数

```python
# 疾病进展参数
settings.set('ModelParameters.PolypIncidenceRate', 0.02)
settings.set('ModelParameters.CancerProgressionRate', 0.05)

# 筛查敏感性
settings.set('Screening.Colonoscopysensitivity', 0.95)
settings.set('Screening.FITSensitivity', 0.75)
```

## 测试和验证

### 运行测试

```bash
# 运行所有测试
python -m pytest tests/ -v

# 运行性能测试
python -m pytest tests/performance/ -v

# 运行基准测试
python tests/performance/test_performance_optimization.py benchmark
```

### 验证安装

```bash
# 运行示例
python examples/performance_optimization_demo.py

# 验证核心功能
python -c "from cmost.core.simulation import Simulation; print('CMOST安装成功')"
```

## 技术架构

### 1. 核心架构设计

CMOST采用模块化设计，主要包括以下层次：

- **表示层**: CLI命令行界面和GUI图形界面
- **业务逻辑层**: 仿真引擎、筛查策略、校准算法
- **数据模型层**: 患者、疾病、筛查等数据模型
- **基础设施层**: 性能优化、并行处理、缓存系统

### 2. 疾病进展模型

CMOST实现了两条主要的癌变通路：

#### 腺瘤-癌通路
```
正常上皮 → 小腺瘤 → 中腺瘤 → 大腺瘤 → 高级别异型增生 → 浸润性癌
```

#### 锯齿状腺瘤-癌通路
```
正常上皮 → 增生性息肉 → 无柄锯齿状腺瘤 → 异型增生 → 浸润性癌
```

### 3. 分子特征建模

支持以下分子标记物：
- MSI状态 (微卫星不稳定)
- CIMP状态 (CpG岛甲基化表型)
- KRAS突变
- BRAF突变
- TP53突变

### 4. 筛查策略实现

#### 支持的筛查方法
- **结肠镜检查**: 全结肠检查，高敏感性
- **乙状结肠镜**: 远端结肠检查
- **粪便免疫化学试验(FIT)**: 非侵入性筛查
- **多靶点粪便DNA检测**: 高敏感性分子检测

#### 动态筛查调度
基于个体风险评估的智能调度：
```python
# 风险评估因子
risk_factors = {
    'age': patient.age,
    'family_history': patient.family_history,
    'previous_findings': patient.screening_history,
    'lifestyle_factors': patient.lifestyle_risk
}

# 计算个体风险评分
risk_score = risk_calculator.calculate_risk(risk_factors)

# 确定下次筛查间隔
next_interval = scheduler.get_optimal_interval(risk_score)
```

## 性能优化详解

### 1. 并行处理架构

CMOST支持多层次并行处理：

#### 患者级并行
```python
# 将患者分批并行处理
patient_batches = chunk_patients(patients, batch_size=1000)
results = parallel_map(process_patient_batch, patient_batches)
```

#### 仿真级并行
```python
# 并行运行多个仿真场景
scenarios = generate_scenarios(parameter_space)
results = parallel_simulation(run_scenario, scenarios)
```

#### 集群级并行
```python
# 提交到计算集群
job_manager.submit_parameter_sweep(
    parameter_space,
    cluster_type='slurm',
    nodes=10,
    cores_per_node=16
)
```

### 2. 内存优化策略

#### 对象池管理
```python
# 患者对象池
patient_pool = memory_manager.create_pool(
    'patients',
    factory=Patient,
    max_size=10000,
    initial_size=1000
)

# 使用托管对象
with managed_object('patients') as patient:
    patient.simulate_year()
```

#### 延迟加载
```python
# 大型数据集延迟加载
class LazyDataset:
    def __getitem__(self, index):
        if index not in self._cache:
            self._cache[index] = self._load_data(index)
        return self._cache[index]
```

### 3. 缓存策略

#### 多级缓存架构
- **L1缓存**: 内存LRU缓存，快速访问
- **L2缓存**: 持久化缓存，跨会话保持
- **L3缓存**: 分布式缓存，集群共享

#### 智能缓存预热
```python
# 预热常用计算结果
preload_keys = generate_common_parameter_combinations()
preload_cache(cache, expensive_calculation, preload_keys)
```

## 校准和验证

### 1. 多方法集成校准

CMOST集成了多种校准方法：

#### 深度神经网络校准
```python
from cmost.calibration.deep_neural_network import DNNCalibrator

calibrator = DNNCalibrator(
    hidden_layers=[128, 64, 32],
    activation='relu',
    optimizer='adam'
)

best_params = calibrator.calibrate(target_data, parameter_bounds)
```

#### 贝叶斯优化校准
```python
from cmost.ml.adaptive_calibration import AdaptiveCalibrator

calibrator = AdaptiveCalibrator(method='bayesian')
result = calibrator.optimize(
    objective_function,
    parameter_space,
    n_iterations=100
)
```

#### 自动方法选择
```python
# 自动选择最佳校准方法
best_method = auto_select_calibration_method(
    target_data,
    available_methods=['neural_network', 'random_forest', 'bayesian'],
    validation_split=0.2
)
```

### 2. 验证框架

#### 交叉验证
```python
# K折交叉验证
cv_scores = cross_validate_model(
    model,
    data,
    cv=5,
    scoring=['accuracy', 'precision', 'recall']
)
```

#### 外部数据验证
```python
# 使用SEER数据验证
validation_result = validate_against_seer(
    simulation_results,
    seer_data,
    metrics=['incidence', 'mortality', 'survival']
)
```

## 扩展和定制

### 1. 自定义疾病模型

```python
from cmost.models.base import DiseaseModel

class CustomDiseaseModel(DiseaseModel):
    def get_progression_probability(self, stage, age, risk_factors):
        # 自定义进展概率计算
        return custom_probability_calculation(stage, age, risk_factors)

    def get_regression_probability(self, stage, treatment):
        # 自定义回归概率计算
        return custom_regression_calculation(stage, treatment)
```

### 2. 自定义筛查策略

```python
from cmost.screening.base import ScreeningStrategy

class CustomScreeningStrategy(ScreeningStrategy):
    def should_screen(self, patient, current_year):
        # 自定义筛查决策逻辑
        return custom_screening_decision(patient, current_year)

    def get_detection_probability(self, lesion, test_type):
        # 自定义检测概率
        return custom_detection_probability(lesion, test_type)
```

### 3. 插件系统

```python
# 注册自定义插件
from cmost.plugins import register_plugin

@register_plugin('custom_analysis')
class CustomAnalysisPlugin:
    def analyze(self, simulation_results):
        # 自定义分析逻辑
        return custom_analysis_results
```

## 部署和运维

### 1. 容器化部署

```dockerfile
# Dockerfile
FROM python:3.9-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
RUN pip install -e .

CMD ["cmost", "serve", "--host", "0.0.0.0", "--port", "8000"]
```

### 2. 集群部署

```yaml
# kubernetes deployment
apiVersion: apps/v1
kind: Deployment
metadata:
  name: cmost-simulation
spec:
  replicas: 3
  selector:
    matchLabels:
      app: cmost
  template:
    metadata:
      labels:
        app: cmost
    spec:
      containers:
      - name: cmost
        image: cmost:latest
        resources:
          requests:
            memory: "4Gi"
            cpu: "2"
          limits:
            memory: "8Gi"
            cpu: "4"
```

### 3. 监控和日志

```python
# 配置监控
from cmost.monitoring import setup_monitoring

setup_monitoring(
    metrics_endpoint='http://prometheus:9090',
    log_level='INFO',
    performance_tracking=True
)
```

## 贡献指南

### 开发环境设置

```bash
git clone https://github.com/your-repo/cmost.git
cd cmost
pip install -e .[dev]
pre-commit install
```

### 代码规范

- 遵循PEP 8编码规范
- 使用4空格缩进
- 行长度不超过88字符
- 添加类型注解
- 编写单元测试

### 提交流程

1. Fork项目
2. 创建功能分支
3. 编写代码和测试
4. 运行测试确保通过
5. 提交Pull Request

## 许可证

本项目采用MIT许可证，详见LICENSE文件。

## 联系方式

- 项目主页: https://github.com/your-repo/cmost
- 文档: https://cmost.readthedocs.io
- 问题反馈: https://github.com/your-repo/cmost/issues
- 邮箱: <EMAIL>

## 致谢

感谢所有为CMOST项目做出贡献的研究人员和开发者。

---

*CMOST - 让结直肠癌筛查策略评估更加精准和高效*
