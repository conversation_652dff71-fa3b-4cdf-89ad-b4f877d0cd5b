{"tests/unit/test_settings_enhanced.py::TestSettingsEnhanced::test_has_setting": true, "tests/unit/test_settings_enhanced.py::TestSettingsEnhanced::test_remove_setting": true, "tests/unit/test_settings_enhanced.py::TestSettingsEnhanced::test_clear_settings": true, "tests/unit/test_settings_enhanced.py::TestSettingsEnhanced::test_merge_settings": true, "tests/unit/test_settings_enhanced.py::TestSettingsEnhanced::test_validate_settings": true, "tests/unit/test_settings_enhanced.py::TestSettingsEnhanced::test_to_dict_conversion": true, "tests/unit/test_settings_enhanced.py::TestSettingsEnhanced::test_from_dict_creation": true, "tests/unit/test_settings_enhanced.py::TestSettingsEnhanced::test_environment_variable_substitution": true, "tests/unit/test_settings_enhanced.py::TestSettingsEnhanced::test_settings_inheritance": true, "tests/unit/test_integrated_calibration.py::TestCalibrationResult": true, "tests/unit/test_integrated_calibration.py::TestIntegratedCalibrator": true, "tests/unit/test_integrated_calibration.py::TestRunIntegratedCalibration": true, "tests/unit/test_integrated_calibration.py::TestIntegratedCalibrator::test_calibrate_with_dnn_failure": true, "tests/unit/test_integrated_calibration.py::TestIntegratedCalibrator::test_calibrate_with_dnn_success": true, "tests/unit/test_models.py::TestPatient::test_patient_creation": true, "tests/unit/test_models.py::TestPatient::test_patient_risk_calculation": true, "tests/unit/test_simulation.py::TestSimulation::test_simulation_initialization": true, "tests/unit/test_simulation.py::TestSimulation::test_population_initialization": true, "tests/unit/test_simulation.py::TestSimulation::test_age_distribution_sampling": true, "tests/unit/test_simulation.py::TestSimulation::test_risk_factor_assignment": true, "tests/unit/test_simulation.py::TestSimulation::test_simulation_run": true, "tests/unit/test_simulation.py::TestSimulation::test_screening_year_detection": true, "tests/unit/test_simulation.py::TestSimulation::test_natural_death_check": true, "tests/unit/test_simulation.py::TestSimulation::test_polyp_generation": true, "tests/unit/test_simulation.py::TestSimulation::test_summary_statistics": true, "tests/unit/test_simulation.py::TestSimulation::test_screening_participation": true}