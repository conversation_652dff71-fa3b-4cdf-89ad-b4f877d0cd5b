["tests/unit/test_adaptive_calibration.py::TestAdaptiveCalibrator::test_calibration_summary", "tests/unit/test_adaptive_calibration.py::TestAdaptiveCalibrator::test_calibration_with_multiple_targets", "tests/unit/test_adaptive_calibration.py::TestAdaptiveCalibrator::test_calibration_with_single_target", "tests/unit/test_adaptive_calibration.py::TestAdaptiveCalibrator::test_calibrator_initialization", "tests/unit/test_adaptive_calibration.py::TestAdaptiveCalibrator::test_parameter_bounds_update", "tests/unit/test_adaptive_calibration.py::TestAdaptiveCalibrator::test_parameter_effect_simulation", "tests/unit/test_adaptive_calibration.py::TestCalibrationData::test_calibration_data_creation", "tests/unit/test_adaptive_calibration.py::TestCalibrationData::test_calibration_data_validation", "tests/unit/test_adaptive_calibration.py::TestCalibrationData::test_calibration_data_with_features", "tests/unit/test_adaptive_calibration.py::TestCalibrationData::test_error_metrics", "tests/unit/test_adaptive_calibration.py::TestCalibrationData::test_residuals_calculation", "tests/unit/test_adaptive_calibration.py::TestParameterSpace::test_discrete_parameter_space", "tests/unit/test_adaptive_calibration.py::TestParameterSpace::test_discrete_sampling", "tests/unit/test_adaptive_calibration.py::TestParameterSpace::test_discrete_value_clipping", "tests/unit/test_adaptive_calibration.py::TestParameterSpace::test_parameter_space_creation", "tests/unit/test_adaptive_calibration.py::TestParameterSpace::test_random_sampling", "tests/unit/test_adaptive_calibration.py::TestParameterSpace::test_value_clipping", "tests/unit/test_adaptive_calibration.py::TestRandomForestOptimizer::test_optimization_simple_function", "tests/unit/test_adaptive_calibration.py::TestRandomForestOptimizer::test_optimizer_initialization", "tests/unit/test_adaptive_calibration.py::TestRandomForestOptimizer::test_random_parameter_sampling", "tests/unit/test_birth_cohort.py::TestBirthCohort::test_birth_cohort_initialization", "tests/unit/test_birth_cohort.py::TestBirthCohort::test_birth_cohort_post_init", "tests/unit/test_birth_cohort.py::TestBirthCohortIntegration::test_full_simulation_workflow", "tests/unit/test_birth_cohort.py::TestBirthCohortSimulation::test_add_annual_birth_cohort", "tests/unit/test_birth_cohort.py::TestBirthCohortSimulation::test_check_cancer_death", "tests/unit/test_birth_cohort.py::TestBirthCohortSimulation::test_check_natural_death", "tests/unit/test_birth_cohort.py::TestBirthCohortSimulation::test_create_birth_cohort", "tests/unit/test_birth_cohort.py::TestBirthCohortSimulation::test_generate_birth_risk_factors", "tests/unit/test_birth_cohort.py::TestBirthCohortSimulation::test_generate_new_polyps", "tests/unit/test_birth_cohort.py::TestBirthCohortSimulation::test_get_all_patients", "tests/unit/test_birth_cohort.py::TestBirthCohortSimulation::test_get_cohort_statistics", "tests/unit/test_birth_cohort.py::TestBirthCohortSimulation::test_get_results", "tests/unit/test_birth_cohort.py::TestBirthCohortSimulation::test_get_setting", "tests/unit/test_birth_cohort.py::TestBirthCohortSimulation::test_import_existing_patients", "tests/unit/test_birth_cohort.py::TestBirthCohortSimulation::test_initialization", "tests/unit/test_birth_cohort.py::TestBirthCohortSimulation::test_run_simulation", "tests/unit/test_birth_cohort.py::TestBirthCohortSimulation::test_simulate_year", "tests/unit/test_birth_cohort.py::TestBirthCohortSimulation::test_update_cohort_risk_factors", "tests/unit/test_dual_architecture.py::TestArchitectureConfig::test_custom_config", "tests/unit/test_dual_architecture.py::TestArchitectureConfig::test_default_config", "tests/unit/test_dual_architecture.py::TestDualArchitectureIntegration::test_hybrid_mode_configuration", "tests/unit/test_dual_architecture.py::TestDualArchitectureIntegration::test_mode_switching_configuration", "tests/unit/test_dual_architecture.py::TestDualArchitectureManager::test_combine_simulation_results", "tests/unit/test_dual_architecture.py::TestDualArchitectureManager::test_get_current_mode", "tests/unit/test_dual_architecture.py::TestDualArchitectureManager::test_get_simulation_summary", "tests/unit/test_dual_architecture.py::TestDualArchitectureManager::test_initialization_birth_cohort", "tests/unit/test_dual_architecture.py::TestDualArchitectureManager::test_initialization_hybrid", "tests/unit/test_dual_architecture.py::TestDualArchitectureManager::test_initialization_natural_population", "tests/unit/test_dual_architecture.py::TestDualArchitectureManager::test_manual_mode_switch", "tests/unit/test_dual_architecture.py::TestDualArchitectureManager::test_single_mode_simulation_birth_cohort", "tests/unit/test_dual_architecture.py::TestDualArchitectureManager::test_single_mode_simulation_natural", "tests/unit/test_dual_architecture.py::TestDualArchitectureManager::test_switch_to_same_mode", "tests/unit/test_dual_architecture.py::TestSimulationModeEnum::test_enum_comparison", "tests/unit/test_dual_architecture.py::TestSimulationModeEnum::test_enum_values", "tests/unit/test_dynamic_scheduling.py::TestDynamicSchedulingRules::test_custom_rules", "tests/unit/test_dynamic_scheduling.py::TestDynamicSchedulingRules::test_default_rules", "tests/unit/test_dynamic_scheduling.py::TestDynamicScreeningScheduler::test_get_recommended_test_first_time", "tests/unit/test_dynamic_scheduling.py::TestDynamicScreeningScheduler::test_get_recommended_test_with_history", "tests/unit/test_dynamic_scheduling.py::TestDynamicScreeningScheduler::test_interval_calculation_no_findings", "tests/unit/test_dynamic_scheduling.py::TestDynamicScreeningScheduler::test_interval_calculation_poor_quality", "tests/unit/test_dynamic_scheduling.py::TestDynamicScreeningScheduler::test_interval_calculation_with_findings", "tests/unit/test_dynamic_scheduling.py::TestDynamicScreeningScheduler::test_is_screening_due_first_time", "tests/unit/test_dynamic_scheduling.py::TestDynamicScreeningScheduler::test_is_screening_due_with_history", "tests/unit/test_dynamic_scheduling.py::TestDynamicScreeningScheduler::test_risk_assessment_caching", "tests/unit/test_dynamic_scheduling.py::TestDynamicScreeningScheduler::test_risk_assessment_high_risk", "tests/unit/test_dynamic_scheduling.py::TestDynamicScreeningScheduler::test_risk_assessment_low_risk", "tests/unit/test_dynamic_scheduling.py::TestDynamicScreeningScheduler::test_risk_assessment_lynch_syndrome", "tests/unit/test_dynamic_scheduling.py::TestDynamicScreeningScheduler::test_schedule_next_screening", "tests/unit/test_dynamic_scheduling.py::TestDynamicScreeningScheduler::test_schedule_next_screening_elderly", "tests/unit/test_dynamic_scheduling.py::TestDynamicScreeningScheduler::test_screening_summary", "tests/unit/test_dynamic_scheduling.py::TestScreeningEvent::test_screening_event_creation", "tests/unit/test_enhanced_disease_model.py::TestGeneticProfile::test_age_specific_risk_fap", "tests/unit/test_enhanced_disease_model.py::TestGeneticProfile::test_age_specific_risk_lynch", "tests/unit/test_enhanced_disease_model.py::TestGeneticProfile::test_default_genetic_profile", "tests/unit/test_enhanced_disease_model.py::TestGeneticProfile::test_fap_syndrome_classification", "tests/unit/test_enhanced_disease_model.py::TestGeneticProfile::test_lynch_syndrome_classification", "tests/unit/test_enhanced_disease_model.py::TestGeneticProfile::test_map_syndrome_classification", "tests/unit/test_enhanced_disease_model.py::TestGeneticProfile::test_polyp_risk_modifier", "tests/unit/test_enhanced_disease_model.py::TestGeneticProfile::test_screening_recommendations", "tests/unit/test_enhanced_disease_model.py::TestMolecularProfile::test_default_molecular_profile", "tests/unit/test_enhanced_disease_model.py::TestMolecularProfile::test_molecular_interactions", "tests/unit/test_enhanced_disease_model.py::TestMolecularProfile::test_progression_modifier", "tests/unit/test_enhanced_disease_model.py::TestMolecularProfile::test_serialization", "tests/unit/test_enhanced_disease_model.py::TestMolecularProfile::test_serrated_pathway_classification", "tests/unit/test_enhanced_disease_model.py::TestMolecularProfile::test_traditional_pathway_classification", "tests/unit/test_enhanced_disease_model.py::TestMolecularProfile::test_treatment_response_modifier", "tests/unit/test_enhanced_disease_model.py::TestMolecularProfileGenerator::test_adenoma_profile_generation", "tests/unit/test_enhanced_disease_model.py::TestMolecularProfileGenerator::test_age_effect_on_mutations", "tests/unit/test_enhanced_disease_model.py::TestMolecularProfileGenerator::test_serrated_profile_generation", "tests/unit/test_enhanced_disease_model.py::TestPatientGeneticIntegration::test_genetic_risk_calculation", "tests/unit/test_enhanced_disease_model.py::TestPatientGeneticIntegration::test_patient_genetic_profile_initialization", "tests/unit/test_enhanced_disease_model.py::TestPatientGeneticIntegration::test_polyp_molecular_profile_integration", "tests/unit/test_enhanced_disease_model.py::TestPatientGeneticIntegration::test_serrated_lesion_molecular_profile_integration", "tests/unit/test_health_economics.py::TestCostData::test_cost_data_creation", "tests/unit/test_health_economics.py::TestCostData::test_cost_data_with_components", "tests/unit/test_health_economics.py::TestCostData::test_cost_discounting", "tests/unit/test_health_economics.py::TestCostData::test_cost_no_discounting_base_year", "tests/unit/test_health_economics.py::TestHealthEconomicsEvaluator::test_add_cost", "tests/unit/test_health_economics.py::TestHealthEconomicsEvaluator::test_add_qaly", "tests/unit/test_health_economics.py::TestHealthEconomicsEvaluator::test_add_qaly_custom_utility", "tests/unit/test_health_economics.py::TestHealthEconomicsEvaluator::test_calculate_patient_outcomes", "tests/unit/test_health_economics.py::TestHealthEconomicsEvaluator::test_calculate_population_outcomes", "tests/unit/test_health_economics.py::TestHealthEconomicsEvaluator::test_compare_strategies", "tests/unit/test_health_economics.py::TestHealthEconomicsEvaluator::test_compare_strategies_dominance", "tests/unit/test_health_economics.py::TestHealthEconomicsEvaluator::test_cost_breakdown_specific_patients", "tests/unit/test_health_economics.py::TestHealthEconomicsEvaluator::test_evaluator_initialization", "tests/unit/test_health_economics.py::TestHealthEconomicsEvaluator::test_get_cost_breakdown", "tests/unit/test_health_economics.py::TestHealthEconomicsEvaluator::test_standard_cost_values", "tests/unit/test_health_economics.py::TestHealthEconomicsEvaluator::test_standard_utility_values", "tests/unit/test_health_economics.py::TestQualityAdjustedLifeYear::test_qaly_calculation", "tests/unit/test_health_economics.py::TestQualityAdjustedLifeYear::test_qaly_creation", "tests/unit/test_health_economics.py::TestQualityAdjustedLifeYear::test_qaly_discounting", "tests/unit/test_integrated_calibration.py::TestCalibrationResult::test_calibration_result_creation", "tests/unit/test_integrated_calibration.py::TestCalibrationResult::test_calibration_result_summary", "tests/unit/test_integrated_calibration.py::TestIntegratedCalibrator::test_calculate_overall_error", "tests/unit/test_integrated_calibration.py::TestIntegratedCalibrator::test_calibrate_with_adaptive_success", "tests/unit/test_integrated_calibration.py::TestIntegratedCalibrator::test_calibrate_with_dnn_failure", "tests/unit/test_integrated_calibration.py::TestIntegratedCalibrator::test_calibrate_with_dnn_success", "tests/unit/test_integrated_calibration.py::TestIntegratedCalibrator::test_calibrator_initialization", "tests/unit/test_integrated_calibration.py::TestIntegratedCalibrator::test_comparison_report_empty", "tests/unit/test_integrated_calibration.py::TestIntegratedCalibrator::test_comparison_report_with_results", "tests/unit/test_integrated_calibration.py::TestIntegratedCalibrator::test_default_parameter_spaces", "tests/unit/test_integrated_calibration.py::TestIntegratedCalibrator::test_prepare_calibration_data", "tests/unit/test_integrated_calibration.py::TestIntegratedCalibrator::test_save_results", "tests/unit/test_integrated_calibration.py::TestRunIntegratedCalibration::test_run_with_auto_select", "tests/unit/test_integrated_calibration.py::TestRunIntegratedCalibration::test_run_with_specific_method", "tests/unit/test_integrated_calibration.py::TestRunIntegratedCalibration::test_run_with_unsupported_method", "tests/unit/test_models.py::TestCancer::test_cancer_creation", "tests/unit/test_models.py::TestCancer::test_cancer_from_polyp", "tests/unit/test_models.py::TestCancer::test_cancer_survival_estimation", "tests/unit/test_models.py::TestCancer::test_cancer_treatment", "tests/unit/test_models.py::TestPatient::test_patient_age_progression", "tests/unit/test_models.py::TestPatient::test_patient_alive_status", "tests/unit/test_models.py::TestPatient::test_patient_creation", "tests/unit/test_models.py::TestPatient::test_patient_risk_calculation", "tests/unit/test_models.py::TestPolyp::test_polyp_advanced_status", "tests/unit/test_models.py::TestPolyp::test_polyp_creation", "tests/unit/test_models.py::TestPolyp::test_polyp_diminutive_status", "tests/unit/test_screening_strategies.py::TestScreeningStrategy::test_parallel_strategy", "tests/unit/test_screening_strategies.py::TestScreeningStrategy::test_sequential_strategy", "tests/unit/test_screening_strategies.py::TestScreeningStrategy::test_single_test_strategy", "tests/unit/test_screening_strategies.py::TestScreeningStrategyManager::test_add_custom_strategy", "tests/unit/test_screening_strategies.py::TestScreeningStrategyManager::test_age_eligibility", "tests/unit/test_screening_strategies.py::TestScreeningStrategyManager::test_detection_range_limitations", "tests/unit/test_screening_strategies.py::TestScreeningStrategyManager::test_get_predefined_strategy", "tests/unit/test_screening_strategies.py::TestScreeningStrategyManager::test_lesion_detection", "tests/unit/test_screening_strategies.py::TestScreeningStrategyManager::test_manager_initialization", "tests/unit/test_screening_strategies.py::TestScreeningStrategyManager::test_parallel_strategy_application", "tests/unit/test_screening_strategies.py::TestScreeningStrategyManager::test_polyp_classification", "tests/unit/test_screening_strategies.py::TestScreeningStrategyManager::test_risk_stratified_strategy_application", "tests/unit/test_screening_strategies.py::TestScreeningStrategyManager::test_sequential_strategy_application", "tests/unit/test_screening_strategies.py::TestScreeningStrategyManager::test_single_test_strategy_application", "tests/unit/test_screening_strategies.py::TestScreeningStrategyManager::test_unknown_strategy_error", "tests/unit/test_screening_strategies.py::TestTestCharacteristics::test_colonoscopy_characteristics", "tests/unit/test_screening_strategies.py::TestTestCharacteristics::test_fit_characteristics", "tests/unit/test_screening_strategies.py::TestTestCharacteristics::test_sigmoidoscopy_characteristics", "tests/unit/test_serrated_lesions.py::TestPatientSerratedLesionIntegration::test_add_serrated_lesion", "tests/unit/test_serrated_lesions.py::TestPatientSerratedLesionIntegration::test_get_advanced_serrated_lesions", "tests/unit/test_serrated_lesions.py::TestPatientSerratedLesionIntegration::test_get_serrated_lesions_by_location", "tests/unit/test_serrated_lesions.py::TestPatientSerratedLesionIntegration::test_remove_serrated_lesion", "tests/unit/test_serrated_lesions.py::TestSerratedLesion::test_cancer_risk_level", "tests/unit/test_serrated_lesions.py::TestSerratedLesion::test_is_advanced_property", "tests/unit/test_serrated_lesions.py::TestSerratedLesion::test_is_large_property", "tests/unit/test_serrated_lesions.py::TestSerratedLesion::test_lesion_creation", "tests/unit/test_serrated_lesions.py::TestSerratedLesion::test_screening_detectability", "tests/unit/test_serrated_lesions.py::TestSerratedLesion::test_to_cancer_conversion", "tests/unit/test_serrated_lesions.py::TestSerratedLesion::test_to_dict_and_from_dict", "tests/unit/test_serrated_lesions.py::TestSerratedLesionProgression::test_lesion_progression_integration", "tests/unit/test_serrated_lesions.py::TestSerratedLesionProgression::test_progression_model_method", "tests/unit/test_settings_enhanced.py::TestSettingsEnhanced::test_clear_settings", "tests/unit/test_settings_enhanced.py::TestSettingsEnhanced::test_environment_variable_substitution", "tests/unit/test_settings_enhanced.py::TestSettingsEnhanced::test_from_dict_creation", "tests/unit/test_settings_enhanced.py::TestSettingsEnhanced::test_get_all_settings", "tests/unit/test_settings_enhanced.py::TestSettingsEnhanced::test_get_nested_setting_with_default", "tests/unit/test_settings_enhanced.py::TestSettingsEnhanced::test_has_setting", "tests/unit/test_settings_enhanced.py::TestSettingsEnhanced::test_has_setting_via_get", "tests/unit/test_settings_enhanced.py::TestSettingsEnhanced::test_initialization_with_path", "tests/unit/test_settings_enhanced.py::TestSettingsEnhanced::test_initialization_without_path", "tests/unit/test_settings_enhanced.py::TestSettingsEnhanced::test_load_json_settings", "tests/unit/test_settings_enhanced.py::TestSettingsEnhanced::test_load_settings_encoding_error", "tests/unit/test_settings_enhanced.py::TestSettingsEnhanced::test_load_settings_file_not_found", "tests/unit/test_settings_enhanced.py::TestSettingsEnhanced::test_load_settings_invalid_json", "tests/unit/test_settings_enhanced.py::TestSettingsEnhanced::test_load_settings_permission_error", "tests/unit/test_settings_enhanced.py::TestSettingsEnhanced::test_load_settings_unsupported_format", "tests/unit/test_settings_enhanced.py::TestSettingsEnhanced::test_load_yaml_settings", "tests/unit/test_settings_enhanced.py::TestSettingsEnhanced::test_merge_settings", "tests/unit/test_settings_enhanced.py::TestSettingsEnhanced::test_remove_setting", "tests/unit/test_settings_enhanced.py::TestSettingsEnhanced::test_save_settings_json", "tests/unit/test_settings_enhanced.py::TestSettingsEnhanced::test_save_settings_permission_error", "tests/unit/test_settings_enhanced.py::TestSettingsEnhanced::test_save_settings_yaml", "tests/unit/test_settings_enhanced.py::TestSettingsEnhanced::test_set_nested_setting_complex", "tests/unit/test_settings_enhanced.py::TestSettingsEnhanced::test_setting_operations", "tests/unit/test_settings_enhanced.py::TestSettingsEnhanced::test_settings_as_dict", "tests/unit/test_settings_enhanced.py::TestSettingsEnhanced::test_settings_inheritance", "tests/unit/test_settings_enhanced.py::TestSettingsEnhanced::test_settings_initialization_from_data", "tests/unit/test_settings_enhanced.py::TestSettingsEnhanced::test_settings_manual_merge", "tests/unit/test_settings_enhanced.py::TestSettingsEnhanced::test_settings_path_handling", "tests/unit/test_settings_enhanced.py::TestSettingsEnhanced::test_settings_structure", "tests/unit/test_settings_enhanced.py::TestSettingsEnhanced::test_settings_validation_basic", "tests/unit/test_settings_enhanced.py::TestSettingsEnhanced::test_to_dict_conversion", "tests/unit/test_settings_enhanced.py::TestSettingsEnhanced::test_validate_settings", "tests/unit/test_simulation.py::TestSimulation::test_age_distribution_sampling", "tests/unit/test_simulation.py::TestSimulation::test_natural_death_check", "tests/unit/test_simulation.py::TestSimulation::test_polyp_generation", "tests/unit/test_simulation.py::TestSimulation::test_population_initialization", "tests/unit/test_simulation.py::TestSimulation::test_risk_factor_assignment", "tests/unit/test_simulation.py::TestSimulation::test_screening_participation", "tests/unit/test_simulation.py::TestSimulation::test_screening_year_detection", "tests/unit/test_simulation.py::TestSimulation::test_simulation_initialization", "tests/unit/test_simulation.py::TestSimulation::test_simulation_run", "tests/unit/test_simulation.py::TestSimulation::test_summary_statistics"]