"""
Enhanced unit tests for settings module.
"""

import pytest
import json
import tempfile
import os
from pathlib import Path
from unittest.mock import patch, mock_open

from cmost.config.settings import Settings


class TestSettingsEnhanced:
    """Enhanced test cases for Settings class."""
    
    def test_initialization_with_path(self):
        """Test settings initialization with config path."""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            json.dump({'test_key': 'test_value'}, f)
            temp_path = f.name
        
        try:
            settings = Settings(temp_path)
            assert settings.config_path == temp_path
            assert settings.get('test_key') == 'test_value'
        finally:
            os.unlink(temp_path)
    
    def test_initialization_without_path(self):
        """Test settings initialization without config path."""
        settings = Settings()
        assert settings.config_path is None
        assert isinstance(settings.settings, dict)
    
    def test_load_json_settings(self):
        """Test loading JSON settings file."""
        test_data = {
            'simulation': {'years': 50, 'patients': 10000},
            'model': {'name': 'CMOST', 'version': '2.0'}
        }
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            json.dump(test_data, f)
            temp_path = f.name
        
        try:
            settings = Settings()
            result = settings.load_settings(temp_path)
            
            assert result is True
            assert settings.get('simulation.years') == 50
            assert settings.get('model.name') == 'CMOST'
        finally:
            os.unlink(temp_path)
    
    def test_load_yaml_settings(self):
        """Test loading YAML settings file."""
        yaml_content = '''
        simulation:
          years: 50
          patients: 10000
        model:
          name: CMOST
          version: "2.0"
        '''
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            f.write(yaml_content)
            temp_path = f.name
        
        try:
            settings = Settings()
            with patch('yaml.safe_load') as mock_yaml:
                mock_yaml.return_value = {
                    'simulation': {'years': 50, 'patients': 10000},
                    'model': {'name': 'CMOST', 'version': '2.0'}
                }
                result = settings.load_settings(temp_path)
                
                assert result is True
                assert settings.get('simulation.years') == 50
        finally:
            os.unlink(temp_path)
    
    def test_load_settings_file_not_found(self):
        """Test loading non-existent settings file."""
        settings = Settings()
        result = settings.load_settings('non_existent_file.json')
        assert result is False
    
    def test_load_settings_permission_error(self):
        """Test loading settings with permission error."""
        settings = Settings()
        
        with patch('builtins.open', side_effect=PermissionError("Permission denied")):
            result = settings.load_settings('test.json')
            assert result is False
    
    def test_load_settings_invalid_json(self):
        """Test loading invalid JSON file."""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            f.write('{"invalid": json}')  # Invalid JSON
            temp_path = f.name
        
        try:
            settings = Settings()
            result = settings.load_settings(temp_path)
            assert result is False
        finally:
            os.unlink(temp_path)
    
    def test_load_settings_encoding_error(self):
        """Test loading file with encoding error."""
        settings = Settings()
        
        with patch('builtins.open', side_effect=UnicodeDecodeError('utf-8', b'', 0, 1, 'invalid')):
            result = settings.load_settings('test.json')
            assert result is False
    
    def test_load_settings_unsupported_format(self):
        """Test loading unsupported file format."""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
            f.write('some text')
            temp_path = f.name
        
        try:
            settings = Settings()
            result = settings.load_settings(temp_path)
            assert result is False
        finally:
            os.unlink(temp_path)
    
    def test_save_settings_json(self):
        """Test saving settings to JSON file."""
        settings = Settings()
        settings.set('test.key', 'test_value')
        settings.set('number', 42)
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            temp_path = f.name
        
        try:
            result = settings.save_settings(temp_path)
            assert result is True
            
            # Verify saved content
            with open(temp_path, 'r') as f:
                saved_data = json.load(f)
                assert saved_data['test']['key'] == 'test_value'
                assert saved_data['number'] == 42
        finally:
            os.unlink(temp_path)
    
    def test_save_settings_yaml(self):
        """Test saving settings to YAML file."""
        settings = Settings()
        settings.set('test.key', 'test_value')
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            temp_path = f.name
        
        try:
            with patch('yaml.dump') as mock_yaml:
                result = settings.save_settings(temp_path)
                assert result is True
                mock_yaml.assert_called_once()
        finally:
            os.unlink(temp_path)
    
    def test_save_settings_permission_error(self):
        """Test saving settings with permission error."""
        settings = Settings()
        
        with patch('builtins.open', side_effect=PermissionError("Permission denied")):
            result = settings.save_settings('test.json')
            assert result is False
    
    def test_get_nested_setting_with_default(self):
        """Test getting nested setting with default value."""
        settings = Settings()
        settings.set('level1.level2.level3', 'deep_value')
        
        # Test existing nested setting
        assert settings.get('level1.level2.level3') == 'deep_value'
        
        # Test non-existing nested setting with default
        assert settings.get('level1.level2.nonexistent', 'default') == 'default'
        
        # Test partially existing path
        assert settings.get('level1.nonexistent.level3', 'default') == 'default'
    
    def test_set_nested_setting_complex(self):
        """Test setting complex nested settings."""
        settings = Settings()
        
        # Set multiple nested values
        settings.set('simulation.parameters.population.size', 10000)
        settings.set('simulation.parameters.population.demographics.age_range', [18, 85])
        settings.set('simulation.parameters.screening.methods', ['colonoscopy', 'FIT'])
        
        # Verify structure
        assert settings.get('simulation.parameters.population.size') == 10000
        assert settings.get('simulation.parameters.population.demographics.age_range') == [18, 85]
        assert settings.get('simulation.parameters.screening.methods') == ['colonoscopy', 'FIT']
    
    def test_has_setting(self):
        """Test checking if setting exists."""
        settings = Settings()
        settings.set('existing.setting', 'value')
        
        assert settings.has('existing.setting') is True
        assert settings.has('existing') is True
        assert settings.has('nonexistent.setting') is False
        assert settings.has('existing.nonexistent') is False
    
    def test_remove_setting(self):
        """Test removing settings."""
        settings = Settings()
        settings.set('to.remove', 'value')
        settings.set('to.keep', 'value')
        
        # Remove setting
        result = settings.remove('to.remove')
        assert result is True
        assert settings.has('to.remove') is False
        assert settings.has('to.keep') is True
        
        # Try to remove non-existent setting
        result = settings.remove('nonexistent')
        assert result is False
    
    def test_get_all_settings(self):
        """Test getting all settings."""
        settings = Settings()
        settings.set('key1', 'value1')
        settings.set('nested.key2', 'value2')
        
        all_settings = settings.get_all()
        assert isinstance(all_settings, dict)
        assert all_settings['key1'] == 'value1'
        assert all_settings['nested']['key2'] == 'value2'
    
    def test_clear_settings(self):
        """Test clearing all settings."""
        settings = Settings()
        settings.set('key1', 'value1')
        settings.set('key2', 'value2')
        
        settings.clear()
        assert len(settings.settings) == 0
        assert settings.get('key1') is None
    
    def test_merge_settings(self):
        """Test merging settings from another Settings object."""
        settings1 = Settings()
        settings1.set('key1', 'value1')
        settings1.set('nested.key2', 'value2')
        
        settings2 = Settings()
        settings2.set('key3', 'value3')
        settings2.set('nested.key4', 'value4')
        
        settings1.merge(settings2)
        
        assert settings1.get('key1') == 'value1'
        assert settings1.get('key3') == 'value3'
        assert settings1.get('nested.key2') == 'value2'
        assert settings1.get('nested.key4') == 'value4'
    
    def test_validate_settings(self):
        """Test settings validation."""
        settings = Settings()
        settings.set('Number_patients', 10000)
        settings.set('Simulation.StartYear', 2000)
        settings.set('Simulation.EndYear', 2050)
        
        # Should pass validation
        is_valid, errors = settings.validate()
        assert is_valid is True
        assert len(errors) == 0
        
        # Test invalid settings
        settings.set('Number_patients', -100)  # Invalid negative value
        is_valid, errors = settings.validate()
        assert is_valid is False
        assert len(errors) > 0
    
    def test_to_dict_conversion(self):
        """Test converting settings to dictionary."""
        settings = Settings()
        settings.set('key1', 'value1')
        settings.set('nested.key2', {'subkey': 'subvalue'})
        
        result_dict = settings.to_dict()
        
        assert isinstance(result_dict, dict)
        assert result_dict['key1'] == 'value1'
        assert result_dict['nested']['key2']['subkey'] == 'subvalue'
    
    def test_from_dict_creation(self):
        """Test creating settings from dictionary."""
        data = {
            'key1': 'value1',
            'nested': {
                'key2': 'value2',
                'deep': {
                    'key3': 'value3'
                }
            }
        }
        
        settings = Settings.from_dict(data)
        
        assert settings.get('key1') == 'value1'
        assert settings.get('nested.key2') == 'value2'
        assert settings.get('nested.deep.key3') == 'value3'
    
    def test_environment_variable_substitution(self):
        """Test environment variable substitution in settings."""
        with patch.dict(os.environ, {'TEST_VAR': 'test_value'}):
            settings = Settings()
            settings.set('path', '${TEST_VAR}/data')
            
            # Should substitute environment variable
            expanded = settings.get_expanded('path')
            assert expanded == 'test_value/data'
    
    def test_settings_inheritance(self):
        """Test settings inheritance from parent configurations."""
        # Create parent settings
        parent_settings = Settings()
        parent_settings.set('common.setting', 'parent_value')
        parent_settings.set('parent.only', 'parent_only_value')
        
        # Create child settings that inherit from parent
        child_settings = Settings()
        child_settings.inherit_from(parent_settings)
        child_settings.set('common.setting', 'child_value')  # Override
        child_settings.set('child.only', 'child_only_value')
        
        # Test inheritance
        assert child_settings.get('common.setting') == 'child_value'  # Overridden
        assert child_settings.get('parent.only') == 'parent_only_value'  # Inherited
        assert child_settings.get('child.only') == 'child_only_value'  # Child-specific